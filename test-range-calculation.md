# 测试Range计算功能

这是一个测试文件，用于验证从Slate的Range对象计算FileRange的功能。

## 功能说明

当用户在编辑器中选择文本时，系统会：

1. 检测到selection变化
2. 如果selection是展开的（有选中文本），调用calculateFileRange函数
3. 计算出对应的FileRange对象，包含：
   - start: { line: number, column: number }
   - end: { line: number, column: number }  
   - text: string

## 实现要点

- 检查editor.isDocument，如果是文档模式，只获取选中文本，不计算位置
- 对于非文档模式，通过选区位置实时计算行列位置
- 使用Editor.string()获取从文档开始到选区位置的文本长度作为偏移量
- 通过文本偏移量计算准确的行号和列号
- 避免依赖可能过时的position属性

## 测试方法

1. 在编辑器中选择一段文本
2. 查看控制台输出或range状态
3. 验证计算出的行号、列号和文本是否正确

选择这段文本来测试功能！
