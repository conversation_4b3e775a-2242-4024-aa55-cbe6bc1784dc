# 测试Range计算功能

这是一个测试文件，用于验证从Slate的Range对象计算FileRange的功能。

## 功能说明

当用户在编辑器中选择文本时，系统会：

1. 检测到selection变化
2. 如果selection是展开的（有选中文本），调用calculateFileRange函数
3. 计算出对应的FileRange对象，包含：
   - start: { line: number, column: number }
   - end: { line: number, column: number }  
   - text: string

## 实现要点

- 使用Range.edges()获取起始和结束点
- 通过Node.get()获取对应的节点
- 从节点的position属性获取行列信息
- 如果文本节点没有position，尝试从父节点获取
- 使用Editor.string()获取选中的文本内容

## 测试方法

1. 在编辑器中选择一段文本
2. 查看控制台输出或range状态
3. 验证计算出的行号、列号和文本是否正确

选择这段文本来测试功能！
